import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/viewmodels/like_bloc/like_bloc.dart';
import 'package:room_eight/views/like_view/widgets/like_screen_shimmer.dart';
import 'package:room_eight/widgets/common_widget/app_alert_dialog.dart';
import 'package:room_eight/views/chat/model/search_user_model.dart';

class LikeScreen extends StatefulWidget {
  const LikeScreen({super.key});

  static Widget builder(BuildContext context) => const LikeScreen();

  @override
  State<LikeScreen> createState() => _LikeScreenState();
}

class _LikeScreenState extends State<LikeScreen> with TickerProviderStateMixin {
  static const int _moveOutTabIndex = 0;
  static const int _moveInTabIndex = 1;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // Initialize TabController with Move In tab (index 1) as default
    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: _moveInTabIndex, // Set Move In as default
    );
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadInitialData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bloc = context.read<LikeBloc>();
      // Load Move In data first since it's the default tab
      bloc.add(LoadMoveInData());
      bloc.add(LoadMoveOutData());
      // Set initial tab to Move In
      bloc.add(TabChangedEvent(_moveInTabIndex));
    });
  }

  void _navigateToChat(dynamic user) {
    final userId = _extractUserId(user);
    final chatUser = _createChatUser(user, userId);

    NavigatorService.pushNamed(
      AppRoutes.chatscreen,
      arguments: [chatUser, () {}],
    );
  }

  int? _extractUserId(dynamic user) {
    final userType = user.runtimeType.toString();

    if (userType.contains('MoveOutUser')) {
      return user.messageId ?? user.id;
    } else if (userType.contains('MoveInUser')) {
      return user.toMessageId ?? user.id;
    }
    return user.id;
  }

  SearchUserData _createChatUser(dynamic user, int? userId) {
    return SearchUserData(
      userId: userId,
      name: user.name,
      userName: user.name,
      profileImage: user.profileImage,
    );
  }

  void _onTabChanged(int index) {
    final bloc = context.read<LikeBloc>();
    bloc.add(TabChangedEvent(index));
    _loadDataForTab(index, bloc);
  }

  void _loadDataForTab(int index, LikeBloc bloc) {
    final state = bloc.state;

    if (index == _moveOutTabIndex &&
        state.moveOutData.isEmpty &&
        !state.isLoadingMoveOut) {
      bloc.add(LoadMoveOutData());
    } else if (index == _moveInTabIndex &&
        state.moveInData.isEmpty &&
        !state.isLoadingMoveIn) {
      bloc.add(LoadMoveInData());
    }
  }

  // Move In specific methods
  void _handleAcceptMoveInLike(dynamic user) {
    _showAcceptLikeDialog(user);
  }

  void _handleRejectMoveInLike(dynamic user) {
    if (user.id != null) {
      context.read<LikeBloc>().add(
        AcceptLikeEvent(profileId: user.id!, isAccept: false),
      );
    }
  }

  void _showAcceptLikeDialog(dynamic user) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CustomAlertDialog(
          title: 'Accept Like',
          subtitle:
              'Are you sure you want to accept this like from ${user.name ?? 'this user'}?',
          confirmButtonText: 'Accept',
          cancelButtonText: 'Cancel',
          isLoading: false,
          onConfirmButtonPressed: () {
            Navigator.pop(dialogContext);
            if (user.id != null) {
              context.read<LikeBloc>().add(
                AcceptLikeEvent(
                  profileId: user.id!,
                  isAccept: true,
                  user: user,
                ),
              );
            }
          },
          onCancelButtonPressed: () {
            Navigator.pop(dialogContext);
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).customColors.scaffoldColor,
      body: BlocConsumer<LikeBloc, LikeState>(
        listener: (context, state) {
          // Handle state changes if needed
        },
        builder: (context, state) {
          if (state.isLoadData || state.isAcceptingLike) {
            return const LikeScreenShimmer();
          }

          return _buildMainContent();
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        buildSizedBoxH(50.h),
        _buildTabView(),
        Expanded(child: _buildTabBarView()),
      ],
    );
  }

  Widget _buildTabView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.fillColor,
          borderRadius: BorderRadius.circular(100.r),
        ),
        child: TabBar(
          controller: _tabController,
          onTap: _onTabChanged,
          indicatorSize: TabBarIndicatorSize.tab,
          labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
          labelColor: Theme.of(context).customColors.fillColor,
          dividerColor: Colors.transparent,
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          labelPadding: EdgeInsets.zero,
          indicator: ShapeDecoration(
            color: Theme.of(context).customColors.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(100.r),
            ),
          ),
          tabs: [
            Tab(text: Lang.of(context).lbl_move_out),
            Tab(text: Lang.of(context).lbl_move_in),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      physics: const NeverScrollableScrollPhysics(),
      children: [_buildMoveOutTab(), _buildMoveInTab()],
    );
  }

  // MOVE OUT TAB - Separate UI Method
  Widget _buildMoveOutTab() {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        if (state.isLoadingMoveOut) {
          return const LikeScreenShimmer();
        }

        if (state.moveOutData.isEmpty) {
          return _buildMoveOutEmptyState(
            onRefresh: () => context.read<LikeBloc>().add(LoadMoveOutData()),
          );
        }

        return _buildMoveOutUserList(
          users: state.moveOutData,
          onRefresh: () => context.read<LikeBloc>().add(LoadMoveOutData()),
        );
      },
    );
  }

  Widget _buildMoveOutEmptyState({required VoidCallback onRefresh}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.favorite_border, size: 64.r, color: Colors.grey),
          buildSizedBoxH(16.h),
          Text(
            'No Move Out data available',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey,
              fontSize: 16.sp,
            ),
          ),
          buildSizedBoxH(20.h),
          CustomElevatedButton(onPressed: onRefresh, text: 'Refresh'),
        ],
      ),
    );
  }

  Widget _buildMoveOutUserList({
    required List<dynamic> users,
    required VoidCallback onRefresh,
  }) {
    return RefreshIndicator(
      onRefresh: () async => onRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(bottom: 80.w, left: 16.0, right: 16.0),
          child: Column(
            children: [
              buildSizedBoxH(20.h),
              ...users.map((user) => _buildMoveOutUserCard(user)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMoveOutUserCard(dynamic user) {
    final bool hasStatusTrue = user.status == true;

    return Column(
      children: [
        GestureDetector(
          onTap: hasStatusTrue ? () => _navigateToChat(user) : null,
          child: Container(
            padding: EdgeInsets.all(8.r),
            decoration: _buildMoveOutCardDecoration(hasStatusTrue),
            child: Column(
              children: [
                _buildMoveOutUserImage(user, hasStatusTrue),
                buildSizedBoxH(12.h),
                _buildMoveOutUserInfo(user),
                _buildUserDescription(user),
              ],
            ),
          ),
        ),
        buildSizedBoxH(20.h),
      ],
    );
  }

  BoxDecoration _buildMoveOutCardDecoration(bool hasStatusTrue) {
    if (hasStatusTrue) {
      // Status true: tappable, bright colors
      return BoxDecoration(
        color: Theme.of(context).customColors.fillColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: const [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
        ],
        border: Border.all(
          color: (Theme.of(context).customColors.primaryColor ?? Colors.blue)
              .withValues(alpha: 0.3),
          width: 2,
        ),
      );
    } else {
      // Status false: not tappable, muted colors
      return BoxDecoration(
        color: (Theme.of(context).customColors.fillColor ?? Colors.white)
            .withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: const [
          BoxShadow(color: Colors.black26, blurRadius: 4, offset: Offset(0, 2)),
        ],
        border: Border.all(color: Colors.grey.withValues(alpha: 0.5), width: 1),
      );
    }
  }

  Widget _buildMoveOutUserImage(dynamic user, bool hasStatusTrue) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12.r),
          child: ColorFiltered(
            colorFilter: !hasStatusTrue
                ? const ColorFilter.mode(Colors.grey, BlendMode.saturation)
                : const ColorFilter.mode(
                    Colors.transparent,
                    BlendMode.multiply,
                  ),
            child: CustomImageView(
              imagePath: ApiEndPoint.getImageUrl + (user.profileImage ?? ''),
              height: 180.h,
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
        ),
        // Add overlay for non-tappable cards
        if (!hasStatusTrue)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.r),
                color: Colors.black.withValues(alpha: 0.3),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMoveOutUserInfo(dynamic user) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          user.name?.isNotEmpty == true ? user.name! : 'Unknown User',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
        ),
        // No action buttons for Move Out users - only navigation on card tap
        const SizedBox.shrink(),
      ],
    );
  }

  // MOVE IN TAB - Separate UI Method
  Widget _buildMoveInTab() {
    return BlocBuilder<LikeBloc, LikeState>(
      builder: (context, state) {
        if (state.isLoadingMoveIn) {
          return const LikeScreenShimmer();
        }

        if (state.moveInData.isEmpty) {
          return _buildMoveInEmptyState(
            onRefresh: () => context.read<LikeBloc>().add(LoadMoveInData()),
          );
        }

        return _buildMoveInUserList(
          users: state.moveInData,
          onRefresh: () => context.read<LikeBloc>().add(LoadMoveInData()),
        );
      },
    );
  }

  Widget _buildMoveInEmptyState({required VoidCallback onRefresh}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.favorite, size: 64.r, color: Colors.grey),
          buildSizedBoxH(16.h),
          Text(
            'No Move In data available',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey,
              fontSize: 16.sp,
            ),
          ),
          buildSizedBoxH(20.h),
          CustomElevatedButton(onPressed: onRefresh, text: 'Refresh'),
        ],
      ),
    );
  }

  Widget _buildMoveInUserList({
    required List<dynamic> users,
    required VoidCallback onRefresh,
  }) {
    return RefreshIndicator(
      onRefresh: () async => onRefresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(bottom: 80.w, left: 16.0, right: 16.0),
          child: Column(
            children: [
              buildSizedBoxH(20.h),
              ...users.map((user) => _buildMoveInUserCard(user)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMoveInUserCard(dynamic user) {
    final bool hasStatusTrue = user.status == true;

    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(8.r),
          decoration: _buildMoveInCardDecoration(),
          child: Column(
            children: [
              _buildMoveInUserImage(user),
              buildSizedBoxH(12.h),
              _buildMoveInUserInfo(user, hasStatusTrue),
              _buildUserDescription(user),
            ],
          ),
        ),
        buildSizedBoxH(20.h),
      ],
    );
  }

  BoxDecoration _buildMoveInCardDecoration() {
    return BoxDecoration(
      color: Theme.of(context).customColors.fillColor,
      borderRadius: BorderRadius.circular(16.r),
      boxShadow: const [
        BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
      ],
    );
  }

  Widget _buildMoveInUserImage(dynamic user) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: CustomImageView(
        imagePath: ApiEndPoint.getImageUrl + (user.profileImage ?? ''),
        height: 180.h,
        width: double.infinity,
        fit: BoxFit.cover,
      ),
    );
  }

  Widget _buildMoveInUserInfo(dynamic user, bool hasStatusTrue) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          user.name?.isNotEmpty == true ? user.name! : 'Unknown User',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
        ),
        hasStatusTrue
            ? _buildChatIcon(() => _navigateToChat(user))
            : _buildMoveInActionButtons(),
      ],
    );
  }

  Widget _buildMoveInActionButtons() {
    return Row(
      children: [
        _buildActionButton(
          onTap: () => _handleRejectMoveInLike,
          iconPath: Assets.images.svgs.icons.icClose.path,
        ),
        buildSizedboxW(5.w),
        _buildActionButton(
          onTap: () => _handleAcceptMoveInLike,
          iconPath: Assets.images.svgs.icons.icWirite.path,
        ),
      ],
    );
  }

  Widget _buildChatIcon(VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 30.h,
        width: 30.w,
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.primaryColor,
          borderRadius: BorderRadius.circular(100.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 5.r,
              offset: const Offset(1, 2),
            ),
          ],
        ),
        child: Icon(Icons.chat, color: Colors.white, size: 16.sp),
      ),
    );
  }

  Widget _buildActionButton({
    required VoidCallback onTap,
    required String iconPath,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 30.h,
        width: 30.w,
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.fillColor,
          borderRadius: BorderRadius.circular(100.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade300,
              blurRadius: 5.r,
              offset: const Offset(1, 2),
            ),
          ],
        ),
        child: CustomImageView(
          imagePath: iconPath,
          margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        ),
      ),
    );
  }

  Widget _buildUserDescription(dynamic user) {
    return Row(
      children: [
        Expanded(
          child: Text(
            user.about ?? 'N/A',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontSize: 14.sp,
              color: Theme.of(context).customColors.darkGreytextcolor,
            ),
          ),
        ),
      ],
    );
  }
}
