import 'package:room_eight/core/utils/app_exports.dart';
import 'package:validators/validators.dart';

class AppValidations {
  AppValidations._();

  static String? verificationCodeValidation(
    String? value,
    BuildContext context,
  ) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_emptyVerificationCode;
    }
    return null;
  }

  static String? phoneNumberValidation(
    String? value,
    BuildContext context,
    // Country selectedCountry,
  ) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_emptyPhoneNumber;
    }

    // Remove the country code prefix if present
    // String phoneNumber = value.replaceFirst('+${selectedCountry.phoneCode}', '').trim();
    String phoneNumber = '';

    // Ensure only digits are present
    if (!RegExp(r'^[0-9]+$').hasMatch(phoneNumber)) {
      return Lang.of(context).lbl_invalidPhoneNumber;
    }

    // Define min and max length based on country (you may need to use a country data package)
    int minLength = 7; // Default minimum length (adjust per country if needed)
    int maxLength = 15; // Default maximum length (adjust per country if needed)

    if (phoneNumber.length < minLength || phoneNumber.length > maxLength) {
      return Lang.of(context).lbl_invalidPhoneNumber;
    }

    return null;
  }

  static String? phoneValidation(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    // Add more validation logic as needed
    return null;
  }

  static String? nameValidation(String? value, BuildContext context) {
    if (value == null || value.isEmpty) return Lang.of(context).lbl_emptyName;
    return null;
  }

  static String? passwordValidation(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_emptyPassword;
    }
    return null;
  }

  static String? confPasswordValidation(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_emptyConfPassword;
    }
    return null;
  }

  static String? confirmPasswordValidation(
    String? value,
    String otherPasswordValue,
    BuildContext context,
  ) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_emptyConfirmPassword;
    }
    if (otherPasswordValue.isEmpty) return null;
    if (otherPasswordValue != value) {
      return Lang.of(context).lbl_passwordMismatch;
    }
    return null;
  }

  static String? emailValidation(String? value, BuildContext context) {
    if (value == null || value.isEmpty) return Lang.of(context).lbl_emptyEmail;
    if (!isEmail(value)) return Lang.of(context).lbl_invalidEmail;
    return null;
  }

  /// Validates if the field is not empty
  static String? validateRequired(
    String? value,
    BuildContext context, {
    String? fieldName,
  }) {
    if (value == null || value.trim().isEmpty) {
      final field = fieldName ?? Lang.of(context).lbl_this_field;
      return '$field ${Lang.of(context).lbl_field_required}';
    }
    return null;
  }

  static String? validateOTP(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_please_enter_otp;
    } else if (value.length != 6) {
      return Lang.of(context).lbl_otp_must_be_6_digits;
    } else if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
      return Lang.of(context).lbl_otp_only_digits;
    }
    return null;
  }

  static String? validateCVV(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_cvv_required;
    }

    if (!RegExp(r'^\d{3,4}$').hasMatch(value)) {
      return Lang.of(context).lbl_cvv_3_4_digits;
    }

    return null;
  }

  static String? validateCardholderName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return Lang.of(context).lbl_name_required;
    }

    if (value.trim().length < 3) {
      return Lang.of(context).lbl_enter_full_name_card;
    }

    return null;
  }

  static String? validateExpiryDate(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_expiry_date_required;
    }

    if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(value)) {
      return Lang.of(context).lbl_use_mm_yy_format;
    }

    final parts = value.split('/');
    if (parts.length != 2) {
      return Lang.of(context).lbl_invalid_format;
    }

    final month = int.tryParse(parts[0]);
    final year = int.tryParse(parts[1]);

    if (month == null || year == null) {
      return Lang.of(context).lbl_invalid_date;
    }

    if (month < 1 || month > 12) {
      return Lang.of(context).lbl_month_01_12;
    }
    final currentYear = DateTime.now().year % 100;
    final currentMonth = DateTime.now().month;

    if (year < currentYear || (year == currentYear && month < currentMonth)) {
      return Lang.of(context).lbl_card_expired;
    }

    return null;
  }

  static String? validateCardNumber(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return Lang.of(context).lbl_card_number_required;
    }

    return null;
  }

  String? validateOtp(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter OTP';
    } else if (!RegExp(r'^\d{6}$').hasMatch(value)) {
      return 'Enter a valid 6-digit OTP';
    }
    return null;
  }
}
