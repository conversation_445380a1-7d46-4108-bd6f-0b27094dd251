part of 'chat_bloc.dart';

class ChatState extends Equatable {
  final List<ChatData> chatList;
  final List<SearchUserData> searchuserList;
  final bool searchuserListLoading;
  final List<ChatMessageData> chatMessageList;
  final bool isLoadingMore;
  final bool isloding;
  final ScrollController? scrollController;
  final ChatListModel? chatListModel;
  final bool hasMore;
  final int page;
  final bool allFetch;
  // final TextEditingController? chatController;
  final TextEditingController? searchController;
  final ChatMessageListModel? chatMessageListModel;

  const ChatState({
    this.chatList = const [],
    this.searchuserList = const [],
    this.isLoadingMore = false,
    this.scrollController,
    this.hasMore = false,
    this.searchuserListLoading = false,
    this.page = 1,
    this.allFetch = false,
    this.isloding = false,
    // this.chatController,
    this.chatListModel,
    this.chatMessageListModel,
    this.chatMessageList = const [],
    this.searchController,
  });

  @override
  List<Object?> get props => [
    chatList,
    searchuserList,
    isLoadingMore,
    scrollController,
    hasMore,
    searchuserListLoading,
    page,
    allFetch,
    isloding,
    // chatController,
    chatListModel,
    chatMessageListModel,
    chatMessageList,
    searchController,
  ];

  ChatState copyWith({
    List<ChatData>? chatList,
    bool? isLoadingMore,
    bool? isloding,
    ScrollController? scrollController,
    ChatListModel? chatListModel,
    bool? hasMore,
    bool? searchuserListLoading,
    int? page,
    bool? allFetch,
    // TextEditingController? chatController,
    ChatMessageListModel? chatMessageListModel,
    List<ChatMessageData>? chatMessageList,
    TextEditingController? searchController,
    List<SearchUserData>? searchuserList,
  }) {
    return ChatState(
      allFetch: allFetch ?? this.allFetch,
      chatList: chatList ?? this.chatList,
      searchuserList: searchuserList ?? this.searchuserList,
      hasMore: hasMore ?? this.hasMore,
      searchuserListLoading:
          searchuserListLoading ?? this.searchuserListLoading,
      isloding: isloding ?? this.isloding,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      page: page ?? this.page,
      scrollController: scrollController ?? this.scrollController,
      chatListModel: chatListModel ?? this.chatListModel,
      // chatController: chatController ?? this.chatController,
      chatMessageListModel: chatMessageListModel ?? this.chatMessageListModel,
      chatMessageList: chatMessageList ?? this.chatMessageList,
      searchController: searchController ?? this.searchController,
    );
  }
}
