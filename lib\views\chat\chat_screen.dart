import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:room_eight/core/api_config/endpoints/socket_key.dart';
import 'package:room_eight/core/socket/socket_service.dart';
import 'package:room_eight/views/chat/bloc/chat_bloc.dart';
import 'package:room_eight/widgets/custom_widget/custom_debounce.dart';

import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/core/utils/loading_animation_widget.dart';
import 'package:room_eight/core/utils/string_extension.dart';
import 'package:room_eight/views/chat/model/chat_message_list_model.dart';
import 'package:room_eight/views/chat/widget/date_time_utils.dart';
import 'package:room_eight/views/chat/widget/link_preview.dart';
import 'package:room_eight/views/chat/widget/typing_indicator_widget.dart';

enum TypeWriterStatus { typing, typed }

class ChatScreen extends StatefulWidget {
  final dynamic args;

  const ChatScreen({super.key, this.args});
  static Widget builder(BuildContext context) {
    var args = ModalRoute.of(context)?.settings.arguments;
    return ChatScreen(args: args);
    // BlocProvider<ChatBloc>(
    //   create: (context) =>
    //       ChatBloc(const ChatState(), ChatRepository(apiClient: ApiClient()))
    //         ..add(ChatInitialEvent()),
    //   child: ChatScreen(args: args),
    // );
  }

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  ScrollController? _scrollController;
  bool _showScrollToBottomButton = false;
  final ValueNotifier<String> _inputText = ValueNotifier('');
  ValueNotifier<TypeWriterStatus> composingStatus = ValueNotifier(
    TypeWriterStatus.typed,
  );
  final ImagePicker _imagePicker = ImagePicker();
  // RecorderController? controller;
  ValueNotifier<bool> isRecording = ValueNotifier(false);
  late Debouncer debouncer;
  final ValueNotifier<bool> _showTypingIndicator = ValueNotifier(false);
  ValueListenable<bool> get typingIndicatorNotifier => _showTypingIndicator;
  int lastMassageID = 0;

  bool _isStart = true;

  @override
  void initState() {
    // Debug widget args
    Logger.lOG("ChatScreen widget.args: ${widget.args}");
    Logger.lOG("ChatScreen widget.args[0]: ${widget.args[0]}");
    Logger.lOG("ChatScreen widget.args[0].userId: ${widget.args[0].userId}");

    SocketService.emit(SocketConfig.joinSocket, {
      'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
    });
    SocketService.response(SocketConfig.joinSocket, (joinSocket) {
      Logger.lOG(joinSocket);
    });
    debouncer = Debouncer(const Duration(seconds: 1));
    super.initState();
    _scrollController = ScrollController()..addListener(_scrollListener);

    scheduleMicrotask(
      () => context.read<ChatBloc>().add(
        GetChatMessageListEvent(
          page: 1,
          userId: (widget.args[0].userId ?? 0).toString(),
        ),
      ),
    );
    if (context.read<ChatBloc>().state.chatMessageList.isNotEmpty) {
      context.read<ChatBloc>().state.chatMessageList.clear();
    }

    if (defaultTargetPlatform == TargetPlatform.iOS ||
        defaultTargetPlatform == TargetPlatform.android) {
      // controller = RecorderController();
    }
    _scrollController?.addListener(() {
      if (_scrollController!.offset > 300) {
        // If the user scrolls up 300px
        setState(() {
          _showScrollToBottomButton = true;
        });
      } else {
        setState(() {
          _showScrollToBottomButton = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _scrollController?.dispose();
    debouncer.dispose();
    _isStart = false;
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController?.position.pixels ==
        _scrollController?.position.maxScrollExtent) {
      final state = context.read<ChatBloc>().state;
      if (!state.isLoadingMore) {
        context.read<ChatBloc>().add(
          GetChatMessageListEvent(
            page: state.page + 1,
            userId: widget.args.userId.toString(),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    isMessageScreen = true;

    SocketService.response(SocketConfig.isTyping, (response) {
      if (response != null && response['is_typing'] != null) {
        // Only show typing indicator if it's from the other user (not self)
        final fromUserId = response['from'] ?? 0;
        final currentUserId =
            Prefobj.preferences?.get(Prefkeys.USER_ID) as int?;

        if (fromUserId != currentUserId) {
          _showTypingIndicator.value =
              response['is_typing'] == 1 || response['is_typing'] == "1";
        }
      }
    });
    SocketService.response(SocketConfig.receivemessage, (response) {
      if (mounted) {
        final id = response['id'];
        final message = response['message'];
        final type = response['type'];
        final createdat = response['created_at'];
        final sentby = response['sent_by'];
        context.read<ChatBloc>().add(
          UpdateChatMessageSocketEvent(
            id: id,
            createdat: createdat,
            message: message,
            sentby: sentby,
            type: type,
          ),
        );

        if (_isStart) {
          Future.delayed(Duration(seconds: 2), () {
            if (sentby !=
                (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)) {
              Logger.lOG("touser $touser");
              SocketService.emit(SocketConfig.messageRead, {
                'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
                'to': Prefobj.preferences?.get(Prefkeys.USER_ID),
                'message_id': lastMassageID,
              });
              // context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
            }
          });
          _isStart = false;
        }
        setState(() {});
      }
    });
    SocketService.response(SocketConfig.sendmessage, (response) {
      if (mounted) {
        Logger.lOG("Send message response: $response");

        // Check if response has error
        if (response['status'] == false || response['error'] != null) {
          Logger.lOG("Send message error: ${response['error']}");
          // Handle error case - maybe show a snackbar or retry
          return;
        }

        // Handle success case
        final id = response['id'];
        final message = response['message'];
        final type = response['type'];
        final createdat = response['created_at'];
        final sentby = response['sent_by'];

        // Only proceed if we have valid data
        if (id != null && message != null && sentby != null) {
          context.read<ChatBloc>().add(
            UpdateChatMessageSocketEvent(
              id: id,
              createdat: createdat,
              message: message,
              sentby: sentby,
              type: type,
            ),
          );
        } else {
          Logger.lOG(
            "Invalid response data: id=$id, message=$message, sentby=$sentby",
          );
        }
        setState(() {});
      }
    });

    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
          NavigatorService.goBack();
          widget.args[1]();
          FocusScope.of(context).requestFocus(FocusNode());
          isMessageScreen = false;
          massageUserID = 0;
          touser = 0;
          // context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
          // try {
          //   await widget.args[1]();
          //   context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
          // } catch (e) {
          //   debugPrint("Error in args[1]: $e");
          // }
        }
      },
      child: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          return BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, themeState) {
              // only first time call this socket

              Future.delayed(Duration(seconds: 2), () {
                if (touser ==
                    (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)) {
                  Logger.lOG("touser $touser");
                  SocketService.emit(SocketConfig.messageRead, {
                    'Authorization': Prefobj.preferences?.get(
                      Prefkeys.AUTHTOKEN,
                    ),
                    'to': touser,
                    'message_id': state.chatMessageList.first.id,
                  });
                  // context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
                }
              });
              return Scaffold(
                body: SafeArea(
                  child: Stack(
                    alignment: Alignment.bottomCenter / 1.3.h,
                    children: [
                      Column(
                        children: [
                          _buildchatAppBar(context, themeState, widget.args[0]),
                          Expanded(
                            child: InkWell(
                              focusColor: Colors.transparent,
                              onTap: () {
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 20.0.w,
                                ),
                                child: Column(
                                  children: [
                                    Visibility(
                                      visible: state.isLoadingMore,
                                      child: SizedBox(
                                        height: 50.h,
                                        child: Center(
                                          child: CupertinoActivityIndicator(
                                            color: Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: state.isloding
                                          ? const LoadingAnimationWidget()
                                          : _buildMessageList(
                                              state.chatMessageList,
                                              themeState,
                                            ),
                                    ),
                                    buildSizedBoxH(20.0),
                                    ValueListenableBuilder(
                                      valueListenable: typingIndicatorNotifier,
                                      builder: (context, value, child) =>
                                          TypingIndicator(
                                            showIndicator: value,
                                            userrname: widget.args[0].userName
                                                .toString(),
                                          ),
                                    ),
                                    _buildMessageTextField(state, themeState),
                                    Platform.isIOS
                                        ? buildSizedBoxH(30.0)
                                        : buildSizedBoxH(20.0),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (_showScrollToBottomButton)
                        FloatingActionButton(
                          mini: true,
                          backgroundColor: Theme.of(context).primaryColor,
                          onPressed: () {
                            _scrollToBottom();
                          },
                          child: const Icon(Icons.arrow_downward),
                        ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildchatAppBar(
    BuildContext context,
    ThemeState themeState,
    dynamic args,
  ) {
    return AppBar(
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      automaticallyImplyLeading: false,
      leadingWidth: 30.0.w,
      leading: InkWell(
        onTap: () {
          context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
          NavigatorService.goBack();
          widget.args[1]();
          FocusScope.of(context).requestFocus(FocusNode());
          isMessageScreen = false;
          massageUserID = 0;
          touser = 0;
        },
        child: CustomImageView(
          imagePath: Assets.images.svgs.icons.icBackArrow.path,
          height: 16.0.h,
          margin: EdgeInsets.only(top: 19.0.h, bottom: 19.0.w, left: 10.0.w),
        ),
      ),
      centerTitle: false,
      title: InkWell(
        onTap: () {
          // PersistentNavBarNavigator.pushNewScreen(context,
          //     screen: BlocProvider<UserProfileIdBloc>(
          //       create: (context) => UserProfileIdBloc(
          //         const UserProfileIdState(),
          //         ProfileRepository(apiClient: ApiClient()),
          //       )..add(UserProfileIdInitial()),
          //       child: UserProfileIdScreen(
          //           userId: widget.args[0].userId.toString(),
          //           stackonScreen: true),
          //     ));
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: CustomImageView(
                  radius: BorderRadius.circular(25.r),
                  height: 40.0.h,
                  width: 40.0.w,
                  fit: BoxFit.cover,
                  imagePath:
                      args?.profileImage.toString() == "" ||
                          args?.profileImage.toString() == null
                      ? Assets.images.pngs.other.pngAppLogo.path
                      : "${SocketConfig.mainbaseURL}${args?.profileImage.toString()}",
                  alignment: Alignment.center,
                ),
              ),
            ),
            buildSizedboxW(8),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  args.userName.toString(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 16.0.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageList(
    List<ChatMessageData> messages,
    ThemeState themeState,
  ) {
    final groupedMessages = _groupMessagesByDate(messages);

    return ListView.builder(
      padding: EdgeInsets.zero,
      controller: _scrollController,
      itemCount: groupedMessages.length,
      reverse: true,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        final date = groupedMessages.keys.elementAt(index);
        final messages = groupedMessages[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    date.toString(),
                    style: Theme.of(
                      context,
                    ).textTheme.headlineSmall?.copyWith(fontSize: 11.0.sp),
                  ),
                ),
              ],
            ),
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              reverse: true,
              itemCount: messages.length,
              itemBuilder: (context, messageIndex) {
                lastMassageID = messages.first.id ?? 0;
                return _buildChatBubble(messages[messageIndex], themeState);
              },
            ),
          ],
        );
      },
    );
  }

  void _scrollToBottom() {
    _scrollController?.animateTo(
      0.0, // Scroll to the top as the list is reversed
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Widget _buildChatBubble(ChatMessageData message, ThemeState themeState) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment:
            message.sentBy ==
                (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        children: [
          Visibility(
            visible:
                message.sentBy !=
                (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?),
            child: ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: CustomImageView(
                  radius: BorderRadius.circular(45.r),
                  border: Border.all(
                    color: Theme.of(context).customColors.primaryColor!,
                  ),
                  height: 36.0.h,
                  width: 36.0.w,
                  fit: BoxFit.cover,
                  imagePath:
                      widget.args[0]?.profileImage == null ||
                          widget.args[0]?.profileImage == ''
                      ? Assets.images.pngs.other.pngAppLogo.path
                      : "${SocketConfig.mainbaseURL}${widget.args[0]?.profileImage.toString()}",
                  alignment: Alignment.center,
                ),
              ),
            ),
          ),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 280.w),
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: message.type == 'image' ? 0.w : 12.w,
                vertical: message.type == 'image' ? 0.h : 10.h,
              ),
              margin: EdgeInsets.fromLTRB(5.w, 0.h, 6.w, 2.h),
              decoration: BoxDecoration(
                color:
                    message.sentBy ==
                        (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)
                    ? Theme.of(context).primaryColor.withValues(alpha: 0.2)
                    : ThemeData().customColors.primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(
                    message.sentBy ==
                            (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)
                        ? 15.r
                        : 0.r,
                  ),
                  topRight: Radius.circular(15.r),
                  bottomRight: Radius.circular(
                    message.sentBy ==
                            (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)
                        ? 0.r
                        : 15.r,
                  ),
                  bottomLeft: Radius.circular(15.r),
                ),
              ),
              child: message.type == 'image'
                  ? InkWell(
                      onTap: () {
                        FocusScope.of(context).unfocus();
                        // PersistentNavBarNavigator.pushNewScreen(context,
                        //     screen: FlowkarImagePreview(
                        //       imagepath:
                        //           '${APIEndPoints.mainbaseURL}/${message.message}',
                        //     ));
                      },
                      child: CustomImageView(
                        imagePath:
                            '${SocketConfig.mainbaseURL}/${message.message}',
                        height: 250.0.h,
                        width: 150.0.w,
                        radius: BorderRadius.circular(4.0.r),
                        fit: BoxFit.cover,
                      ),
                    )
                  : message.type == 'custom'
                  ? SizedBox.shrink()
                  : message.message!.isUrl
                  ? LinkPreview(url: message.message ?? '')
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          message.message ?? '',
                          textAlign: TextAlign.start,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                color:
                                    message.sentBy ==
                                        (Prefobj.preferences?.get(
                                              Prefkeys.USER_ID,
                                            )
                                            as int?)
                                    ? ThemeData().customColors.blackColor
                                    : ThemeData().customColors.fillColor,
                                fontSize: 16.0.sp,
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                        // Show pending indicator for sent messages that are still pending
                        if (message.isPending &&
                            message.sentBy ==
                                (Prefobj.preferences?.get(Prefkeys.USER_ID)
                                    as int?))
                          Padding(
                            padding: EdgeInsets.only(top: 4.h),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 12.w,
                                  height: 12.h,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 1.5,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      ThemeData().customColors.primaryColor!
                                          .withValues(alpha: 0.7),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 4.w),
                                Text(
                                  'Sending...',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: ThemeData()
                                            .customColors
                                            .primaryColor!
                                            .withValues(alpha: 0.7),
                                        fontSize: 10.sp,
                                      ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, List<ChatMessageData>> _groupMessagesByDate(
    List<ChatMessageData> messages,
  ) {
    final Map<String, List<ChatMessageData>> groupedMessages = {};
    for (var message in messages) {
      final dateTime = DateTime.parse(
        message.createdAt.toString().split("+").first,
      );
      final formattedDate = dateTime.formatForChatMessage();
      groupedMessages.putIfAbsent(formattedDate, () => []).add(message);
    }
    return groupedMessages;
  }

  Widget _buildMessageTextField(ChatState state, ThemeState themestate) {
    return ValueListenableBuilder<bool>(
      valueListenable: isRecording,
      builder: (_, isRecordingValue, child) {
        return Row(
          children: [
            // if (isRecordingValue && controller != null && !kIsWeb)
            //   Expanded(
            //     child: Container(
            //       decoration: BoxDecoration(
            //           color: AppColors.primaryColor,
            //           borderRadius: BorderRadius.circular(22.0.r)),
            //       child: Padding(
            //         padding: const EdgeInsets.all(4.0),
            //         child: Row(
            //           mainAxisSize: MainAxisSize.min,
            //           children: [
            //             if (isRecordingValue)
            //               CustomImageView(
            //                 height: 55.0.h,
            //                 imagePath:
            //                     Assets.images.icons.icDeleteRecording.path,
            //                 onTap: () {
            //                   _cancelRecording();
            //                 },
            //               ),
            //             Expanded(
            //               child: AudioWaveforms(
            //                 size: const Size(double.infinity, 50),
            //                 recorderController: controller!,
            //                 padding: EdgeInsets.symmetric(horizontal: 8.0.w),
            //                 decoration: BoxDecoration(
            //                     borderRadius: BorderRadius.circular(12.0.r)),
            //                 waveStyle: const WaveStyle(
            //                   extendWaveform: true,
            //                   showMiddleLine: false,
            //                   waveCap: StrokeCap.round,
            //                   waveColor: AppColors.whitecolor,
            //                 ),
            //               ),
            //             ),
            //             Padding(
            //               padding: const EdgeInsets.only(right: 8.0),
            //               child: InkWell(
            //                 onTap: _recordOrStop,
            //                 child: Padding(
            //                   padding: const EdgeInsets.all(8.0),
            //                   child: Text(
            //                     Lang.of(context).lbl_send,
            //                     style: Theme.of(context)
            //                         .textTheme
            //                         .bodySmall
            //                         ?.copyWith(
            //                             color: AppColors.whitecolor,
            //                             fontWeight: FontWeight.bold),
            //                   ),
            //                 ),
            //               ),
            //             ),
            //           ],
            //         ),
            //       ),
            //     ),
            //   ),
            Visibility(
              child: Expanded(
                child: SizedBox(
                  height: 59,
                  child: BlocBuilder<ChatBloc, ChatState>(
                    builder: (context, states) {
                      TextEditingController chatController =
                          TextEditingController();
                      return CustomTextInputField(
                        type: InputType.text,
                        controller: chatController,
                        // controller: states.chatController,
                        hintLabel: 'Enter Message',
                        context: context,
                        onChanged: (inputText) {
                          context.read<ChatBloc>().add(
                            TypingSocketEvent(
                              userId: widget.args[0].userId,
                              isTyping: "1",
                            ),
                          );
                          _inputText.value = inputText;
                        },
                        // prefixIcon: ValueListenableBuilder<String>(
                        //   valueListenable: _inputText,
                        //   builder: (_, inputTextValue, __) {
                        //     if (inputTextValue.isEmpty) {
                        //       return Padding(
                        //         padding: const EdgeInsets.all(6.0),
                        //         child: CustomImageView(
                        //           height: 25.h,
                        //           width: 25.w,
                        //           imagePath:
                        //               Assets.images.pngs.icons.icCamera.path,
                        //           onTap: () =>
                        //               _onIconPressed(ImageSource.camera),
                        //         ),
                        //       );
                        //     } else {
                        //       return SizedBox.shrink();
                        //     }
                        //   },
                        // ),
                        suffixIcon: ValueListenableBuilder<String>(
                          valueListenable: _inputText,
                          builder: (_, inputTextValue, __) {
                            if (inputTextValue.isNotEmpty) {
                              return Padding(
                                padding: EdgeInsets.only(right: 0.0.w),
                                child: InkWell(
                                  onTap: () {
                                    // if (states
                                    //     .chatController!
                                    //     .text
                                    //     .isNotEmpty) {
                                    context.read<ChatBloc>().add(
                                      SendMessageEvent(
                                        message: inputTextValue,
                                        file: '',
                                        touserId:
                                            int.tryParse(
                                              widget.args[0].userId.toString(),
                                            ) ??
                                            0,
                                        type: 'text',
                                      ),
                                    );
                                    setState(() {});
                                    chatController.clear();
                                    // states.chatController?.clear();
                                    _inputText.value = '';
                                    FocusScope.of(
                                      context,
                                    ).requestFocus(FocusNode());
                                    // }
                                  },
                                  child: Icon(
                                    Icons.send_rounded,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                  // Text(
                                  //   // "Lang.of(context).lbl_send",
                                  //   style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
                                  // ),
                                ),
                              );
                            } else {
                              context.read<ChatBloc>().add(
                                TypingSocketEvent(
                                  userId: widget.args[0].userId,
                                  isTyping: "0",
                                ),
                              );
                              return SizedBox.shrink();
                              // Row(
                              //   mainAxisAlignment: MainAxisAlignment.end,
                              //   mainAxisSize: MainAxisSize.min,
                              //   children: [
                              //     Padding(
                              //       padding: EdgeInsets.only(
                              //           top: 18.0.h, bottom: 18.0.h, right: 8.0.w),
                              //       child: CustomImageView(
                              //         color: ThemeData().customColors.fillColor,
                              //         imagePath: '',
                              //         height: 20.h,
                              //         width: 20.w,
                              //         onTap: () =>
                              //             _onIconPressed(ImageSource.gallery),
                              //       ),
                              //     ),
                              //     // Padding(
                              //     //   padding: EdgeInsets.only(
                              //     //       top: 15.0.h, bottom: 15.0.h, right: 15.0.w),
                              //     //   child: CustomImageView(
                              //     //       height: 25.h,
                              //     //       width: 25.0.w,
                              //     //       imagePath:
                              //     //           Assets.images.icons.icMicrophone.path,
                              //     //       onTap: () => _recordOrStop()),
                              //     // ),
                              //   ],
                              // );
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Widget _buildMessageTextField(ChatState state, ThemeState themestate) {
  //   return ValueListenableBuilder<bool>(
  //     valueListenable: isRecording,
  //     builder: (_, isRecordingValue, child) {
  //       return Row(
  //         children: [
  //           // if (isRecordingValue && controller != null && !kIsWeb)
  //           //   Expanded(
  //           //     child: Container(
  //           //       decoration: BoxDecoration(
  //           //           color: AppColors.primaryColor,
  //           //           borderRadius: BorderRadius.circular(22.0.r)),
  //           //       child: Padding(
  //           //         padding: const EdgeInsets.all(4.0),
  //           //         child: Row(
  //           //           mainAxisSize: MainAxisSize.min,
  //           //           children: [
  //           //             if (isRecordingValue)
  //           //               CustomImageView(
  //           //                 height: 55.0.h,
  //           //                 imagePath:
  //           //                     Assets.images.icons.icDeleteRecording.path,
  //           //                 onTap: () {
  //           //                   _cancelRecording();
  //           //                 },
  //           //               ),
  //           //             Expanded(
  //           //               child: AudioWaveforms(
  //           //                 size: const Size(double.infinity, 50),
  //           //                 recorderController: controller!,
  //           //                 padding: EdgeInsets.symmetric(horizontal: 8.0.w),
  //           //                 decoration: BoxDecoration(
  //           //                     borderRadius: BorderRadius.circular(12.0.r)),
  //           //                 waveStyle: const WaveStyle(
  //           //                   extendWaveform: true,
  //           //                   showMiddleLine: false,
  //           //                   waveCap: StrokeCap.round,
  //           //                   waveColor: AppColors.whitecolor,
  //           //                 ),
  //           //               ),
  //           //             ),
  //           //             Padding(
  //           //               padding: const EdgeInsets.only(right: 8.0),
  //           //               child: InkWell(
  //           //                 onTap: _recordOrStop,
  //           //                 child: Padding(
  //           //                   padding: const EdgeInsets.all(8.0),
  //           //                   child: Text(
  //           //                     Lang.of(context).lbl_send,
  //           //                     style: Theme.of(context)
  //           //                         .textTheme
  //           //                         .bodySmall
  //           //                         ?.copyWith(
  //           //                             color: AppColors.whitecolor,
  //           //                             fontWeight: FontWeight.bold),
  //           //                   ),
  //           //                 ),
  //           //               ),
  //           //             ),
  //           //           ],
  //           //         ),
  //           //       ),
  //           //     ),
  //           //   ),
  //           Visibility(
  //             child: Expanded(
  //               child: SizedBox(
  //                 height: 59,
  //                 child: BlocBuilder<ChatBloc, ChatState>(
  //                   builder: (context, state) {
  //                     return CustomTextInputField(
  //                       controller: state.chatController,
  //                       hintLabel: 'Enter Message',
  //                       context: context,
  //                       onChanged: (inputText) {
  //                         context.read<ChatBloc>().add(
  //                           TypingSocketEvent(
  //                             userId: widget.args[0].userId,
  //                             isTyping: "1",
  //                           ),
  //                         );
  //                         _inputText.value = inputText;
  //                       },
  //                       // onChanged: (inputText) {
  //                       //   // Update the ValueNotifier to sync with the text field
  //                       //   _inputText.value = inputText;
  //                       //   Logger.lOG("Text field changed: '$inputText'");
  //                       //   Logger.lOG(
  //                       //     "ValueNotifier updated: '${_inputText.value}'",
  //                       //   );

  //                       //   // Send typing indicator when user starts typing
  //                       //   if (inputText.trim().isNotEmpty) {
  //                       //     context.read<ChatBloc>().add(
  //                       //       TypingSocketEvent(
  //                       //         userId: widget.args[0].userId ?? 0,
  //                       //         isTyping: "1",
  //                       //       ),
  //                       //     );

  //                       //     // Use debouncer to stop typing indicator after user stops typing
  //                       //     debouncer.run(
  //                       //       () {
  //                       //         // Stop typing after 1 second of inactivity
  //                       //         context.read<ChatBloc>().add(
  //                       //           TypingSocketEvent(
  //                       //             userId: widget.args[0].userId ?? 0,
  //                       //             isTyping: "0",
  //                       //           ),
  //                       //         );
  //                       //       },
  //                       //       () {
  //                       //         // This runs when debouncer is reset (user is still typing)
  //                       //       },
  //                       //     );
  //                       //   } else {
  //                       //     // Stop typing immediately when text becomes empty
  //                       //     context.read<ChatBloc>().add(
  //                       //       TypingSocketEvent(
  //                       //         userId: widget.args[0].userId ?? 0,
  //                       //         isTyping: "0",
  //                       //       ),
  //                       //     );
  //                       //   }
  //                       // },
  //                       prefixIcon: ValueListenableBuilder<String>(
  //                         valueListenable: _inputText,
  //                         builder: (_, inputTextValue, __) {
  //                           if (inputTextValue.isEmpty) {
  //                             return CustomImageView(
  //                               margin: EdgeInsets.all(6.0),
  //                               height: 32.h,
  //                               width: 32.w,
  //                               imagePath:
  //                                   Assets.images.pngs.icons.icCamera.path,
  //                               onTap: () => _onIconPressed(ImageSource.camera),
  //                             );
  //                           } else {
  //                             return Padding(
  //                               padding: const EdgeInsets.all(6.0),
  //                               child: CustomImageView(
  //                                 onTap: () {
  //                                   state.chatController?.clear();
  //                                   _inputText.value = '';
  //                                   // Stop typing when clearing text
  //                                   context.read<ChatBloc>().add(
  //                                     TypingSocketEvent(
  //                                       userId: widget.args[0].userId ?? 0,
  //                                       isTyping: "0",
  //                                     ),
  //                                   );
  //                                 },
  //                                 height: 32.h,
  //                                 width: 32.w,
  //                                 imagePath:
  //                                     Assets.images.pngs.icons.icCamera.path,
  //                               ),
  //                             );
  //                           }
  //                         },
  //                       ),
  //                       suffixIcon: ValueListenableBuilder<String>(
  //                         valueListenable: _inputText,
  //                         builder: (_, inputTextValue, __) {
  //                           if (inputTextValue.isNotEmpty) {
  //                             return Padding(
  //                               padding: EdgeInsets.only(right: 0.0.w),
  //                               child: InkWell(
  //                                 onTap: () {
  //                                   // if (state.chatController?.text.isNotEmpty ??
  //                                   //     false) {
  //                                   //   context.read<ChatBloc>().add(
  //                                   //     SendMessageEvent(
  //                                   //       message: state.chatController?.text ?? '',
  //                                   //       file: '',
  //                                   //       touserId:
  //                                   //           int.tryParse(
  //                                   //             widget.args[0].userId.toString(),
  //                                   //           ) ??
  //                                   //           0,
  //                                   //       type: 'text',
  //                                   //     ),
  //                                   //   );
  //                                   //   setState(() {});
  //                                   //   state.chatController?.clear();
  //                                   //   _inputText.value = '';
  //                                   //   FocusScope.of(
  //                                   //     context,
  //                                   //   ).requestFocus(FocusNode());
  //                                   // }

  //                                   // Logger.lOG("=== SEND BUTTON TAPPED ===");

  //                                   // final notifierText = _inputText.value;

  //                                   // // Use the ValueNotifier text since it's properly synchronized
  //                                   // final messageText = notifierText.trim();

  //                                   if (state.chatController?.text.isNotEmpty ??
  //                                       false) {
  //                                     context.read<ChatBloc>().add(
  //                                       SendMessageEvent(
  //                                         message:
  //                                             state.chatController?.text ?? '',
  //                                         file: '',
  //                                         touserId: widget.args[0].userId ?? 0,
  //                                         type: 'text',
  //                                       ),
  //                                     );

  //                                     // Clear the text field using BLoC event
  //                                     context.read<ChatBloc>().add(
  //                                       ClearChatTextFieldEvent(),
  //                                     );
  //                                     setState(() {});
  //                                     state.chatController?.clear();
  //                                     _inputText.value = '';
  //                                     FocusScope.of(
  //                                       context,
  //                                     ).requestFocus(FocusNode());
  //                                   } else {
  //                                     Logger.lOG(
  //                                       "=== MESSAGE IS EMPTY, NOT SENDING ===",
  //                                     );
  //                                   }
  //                                 },
  //                                 child: Icon(
  //                                   Icons.send_rounded,
  //                                   color: Theme.of(context).primaryColor,
  //                                 ),
  //                                 // Text(
  //                                 //   // "Lang.of(context).lbl_send",
  //                                 //   style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
  //                                 // ),
  //                               ),
  //                             );
  //                           } else {
  //                             context.read<ChatBloc>().add(
  //                               TypingSocketEvent(
  //                                 userId: widget.args[0].userId ?? 0,
  //                                 isTyping: "0",
  //                               ),
  //                             );
  //                             // Always show send button, but make it grayed out when empty
  //                             return Padding(
  //                               padding: EdgeInsets.only(right: 0.0.w),
  //                               child: InkWell(
  //                                 onTap: () {
  //                                   Logger.lOG(
  //                                     "=== SEND BUTTON TAPPED (EMPTY STATE) ===",
  //                                   );

  //                                   // Get text from both sources

  //                                   final notifierText = _inputText.value;
  //                                   final messageText = notifierText.trim();

  //                                   if (messageText.isNotEmpty) {
  //                                     Logger.lOG("=== SENDING MESSAGE ===");
  //                                     Logger.lOG(
  //                                       "To user ID: ${widget.args[0].userId}",
  //                                     );
  //                                     Logger.lOG(
  //                                       "Socket connected: ${SocketService.isConnected}",
  //                                     );

  //                                     context.read<ChatBloc>().add(
  //                                       SendMessageEvent(
  //                                         message: messageText,
  //                                         file: '',
  //                                         touserId: widget.args[0].userId ?? 0,
  //                                         type: 'text',
  //                                       ),
  //                                     );

  //                                     // Stop typing indicator when message is sent
  //                                     context.read<ChatBloc>().add(
  //                                       TypingSocketEvent(
  //                                         userId: widget.args[0].userId ?? 0,
  //                                         isTyping: "0",
  //                                       ),
  //                                     );
  //                                     state.chatController?.clear();
  //                                     state.chatController?.text = '';

  //                                     // Clear the text field using BLoC event
  //                                     Logger.lOG("=== CLEARING TEXT FIELD ===");
  //                                     context.read<ChatBloc>().add(
  //                                       const ClearChatTextFieldEvent(),
  //                                     );
  //                                     state.chatController?.clear();
  //                                     state.chatController?.text = '';
  //                                     _inputText.value = '';
  //                                     Logger.lOG(
  //                                       "ValueNotifier cleared: '${_inputText.value}'",
  //                                     );

  //                                     // Remove focus from text field
  //                                     FocusScope.of(context).unfocus();

  //                                     Logger.lOG(
  //                                       "=== MESSAGE SENT AND CLEARED ===",
  //                                     );
  //                                   } else {
  //                                     Logger.lOG("=== NO MESSAGE TO SEND ===");
  //                                   }
  //                                 },
  //                                 child: Icon(
  //                                   Icons.send_rounded,
  //                                   color: inputTextValue.isEmpty
  //                                       ? Colors.grey
  //                                       : Theme.of(context).primaryColor,
  //                                 ),
  //                               ),
  //                             );
  //                             // Row(
  //                             //   mainAxisAlignment: MainAxisAlignment.end,
  //                             //   mainAxisSize: MainAxisSize.min,
  //                             //   children: [
  //                             //     Padding(
  //                             //       padding: EdgeInsets.only(
  //                             //           top: 18.0.h, bottom: 18.0.h, right: 8.0.w),
  //                             //       child: CustomImageView(
  //                             //         color: ThemeData().customColors.fillColor,
  //                             //         imagePath: '',
  //                             //         height: 20.h,
  //                             //         width: 20.w,
  //                             //         onTap: () =>
  //                             //             _onIconPressed(ImageSource.gallery),
  //                             //       ),
  //                             //     ),
  //                             //     // Padding(
  //                             //     //   padding: EdgeInsets.only(
  //                             //     //       top: 15.0.h, bottom: 15.0.h, right: 15.0.w),
  //                             //     //   child: CustomImageView(
  //                             //     //       height: 25.h,
  //                             //     //       width: 25.0.w,
  //                             //     //       imagePath:
  //                             //     //           Assets.images.icons.icMicrophone.path,
  //                             //     //       onTap: () => _recordOrStop()),
  //                             //     // ),
  //                             //   ],
  //                             // );
  //                           }
  //                         },
  //                       ),
  //                       type: InputType.text,
  //                     );
  //                   },
  //                 ),
  //               ),
  //             ),
  //           ),
  //         ],
  //       );
  //     },
  //   );
  // }

  // FutureOr<void> _cancelRecording() async {
  //   assert(
  //     defaultTargetPlatform == TargetPlatform.iOS ||
  //         defaultTargetPlatform == TargetPlatform.android,
  //     "Voice messages are only supported with android and ios platform",
  //   );
  //   if (!isRecording.value) return;
  //   // final path = await controller?.stop();
  //   if (path == null) {
  //     isRecording.value = false;
  //     return;
  //   }
  //   final file = File(path);

  //   if (await file.exists()) {
  //     await file.delete();
  //   }

  //   isRecording.value = false;
  // }

  // Future<void> _recordOrStop() async {
  //   assert(
  //     defaultTargetPlatform == TargetPlatform.iOS ||
  //         defaultTargetPlatform == TargetPlatform.android,
  //     "Voice messages are only supported with android and ios platform",
  //   );
  //   if (!isRecording.value) {
  //     await controller?.record(
  //       androidEncoder: AndroidEncoder.aac,
  //       iosEncoder: IosEncoder.kAudioFormatMPEG4AAC,
  //       androidOutputFormat: AndroidOutputFormat.mpeg4,
  //       bitRate: 128000,
  //       sampleRate: 44100,
  //     );
  //     isRecording.value = true;
  //   } else {
  //     final path = await controller?.stop();
  //     isRecording.value = false;
  //     path;
  //     Logger.lOG(path);
  //     // Check if an image is selected
  //     if (path != null && path.isNotEmpty) {
  //       // Get the MIME type of the image file
  //       String? mimeType = lookupMimeType(path);

  //       if (mimeType != null) {
  //         // Convert the image file to Base64
  //         File audioFile = File(path);
  //         List<int> audioBytes = await audioFile.readAsBytes();
  //         String base64audio = base64Encode(audioBytes);

  //         // Create the Data URI string
  //         String dataUri = 'data:audio/m4a;;base64,$base64audio';

  //         // Send the Data URI as a chat message

  //         context.read<ChatBloc>().add(SendMessageEvent(
  //               file: dataUri,
  //               message: "",
  //               touserId: int.tryParse(widget.args[0].userId.toString()) ?? 0,
  //               type: 'custom', // or any other type indicating an image
  //             ));
  //       } else {
  //         Logger.lOG('Could not determine MIME type.');
  //       }
  //     }
  //   }
  // }

  void _onIconPressed(ImageSource imageSource) async {
    try {
      // Pick an image from the specified image source
      final XFile? image = await _imagePicker.pickImage(
        source: imageSource,
        preferredCameraDevice: CameraDevice.rear,
      );
      if (image != null) {
        // NavigatorService.pushNamed(AppRoutes.chatImagePreview,
        //     arguments: [image, widget.args[0].userId.toString()]);
      }
    } catch (e) {
      Logger.lOG('Error picking/sending image: ${e.toString()}');
    }
  }
}
